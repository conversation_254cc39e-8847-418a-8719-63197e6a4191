#!/usr/bin/env python3
"""
Comprehensive debug script for favorite order deletion issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.validation import validate_firebase_path, sanitize_input
from src.firebase_db import set_data, get_data, update_favorite_orders
from src.data_storage import delete_favorite_order, get_user_favorite_orders
from src.data_models import favorite_orders
from src.config import logger

def test_real_user_scenario():
    """Test with a real user scenario"""
    print("="*60)
    print("COMPREHENSIVE FAVORITE ORDER DELETION DEBUG")
    print("="*60)
    
    # Use a test user ID
    test_user_id = 123456789
    test_user_id_str = str(test_user_id)
    
    # Create test favorite orders
    test_favorites = [
        {
            "favorite_name": "Test Order 1",
            "items": [{"name": "Pizza", "price": 100}],
            "total": 100,
            "timestamp": "2025-09-06 12:00:00"
        },
        {
            "favorite_name": "Test Order 2", 
            "items": [{"name": "Burger", "price": 80}],
            "total": 80,
            "timestamp": "2025-09-06 12:30:00"
        }
    ]
    
    print(f"\n1. Setting up test data for user {test_user_id}")
    
    # Clear local cache first
    favorite_orders.clear()
    print(f"   Cleared local cache: {list(favorite_orders.keys())}")
    
    # Set data in Firebase
    firebase_result = update_favorite_orders(test_user_id_str, test_favorites)
    print(f"   Firebase set result: {firebase_result}")
    
    if not firebase_result:
        print("   ❌ Failed to set up test data in Firebase")
        return
    
    # Verify data is in Firebase
    firebase_data = get_data(f"favorite_orders/{test_user_id_str}")
    print(f"   Firebase verification: {len(firebase_data) if firebase_data else 0} orders found")
    
    print(f"\n2. Testing get_user_favorite_orders function")
    user_favorites = get_user_favorite_orders(test_user_id, sync_data=False)
    print(f"   Retrieved {len(user_favorites)} favorite orders")
    print(f"   Local cache now has: {list(favorite_orders.keys())}")
    
    if test_user_id_str in favorite_orders:
        print(f"   Local cache for user: {len(favorite_orders[test_user_id_str])} orders")
    else:
        print("   ❌ User still not in local cache!")
    
    print(f"\n3. Testing delete_favorite_order function")
    print(f"   Before deletion - orders in cache: {len(favorite_orders.get(test_user_id_str, []))}")
    
    # Try to delete the first order (index 0)
    delete_result = delete_favorite_order(test_user_id, 0)
    print(f"   Delete result: {delete_result}")
    
    if delete_result:
        print("   ✅ Deletion succeeded!")
        
        # Verify deletion
        remaining_firebase = get_data(f"favorite_orders/{test_user_id_str}")
        remaining_local = favorite_orders.get(test_user_id_str, [])
        
        print(f"   Remaining in Firebase: {len(remaining_firebase) if remaining_firebase else 0}")
        print(f"   Remaining in local cache: {len(remaining_local)}")
        
        if remaining_firebase and len(remaining_firebase) == 1:
            print("   ✅ Deletion verified in Firebase")
        else:
            print("   ❌ Deletion not properly reflected in Firebase")
            
    else:
        print("   ❌ Deletion failed!")
        
        # Debug why it failed
        print(f"\n   DEBUGGING FAILURE:")
        print(f"   - User in local cache: {test_user_id_str in favorite_orders}")
        print(f"   - Local cache keys: {list(favorite_orders.keys())}")
        if test_user_id_str in favorite_orders:
            print(f"   - Orders for user: {len(favorite_orders[test_user_id_str])}")
        
        # Try to manually load from Firebase
        print(f"\n   Attempting manual Firebase load...")
        from src.firebase_db import get_favorite_orders as get_firebase_favorite_orders
        manual_data = get_firebase_favorite_orders(test_user_id_str)
        print(f"   Manual Firebase result: {manual_data}")
        
    print(f"\n4. Testing the actual handler flow")
    # Simulate what happens in the handler
    try:
        from src.handlers.favorite_orders_handlers import confirm_delete_favorite_order
        print("   Found confirm_delete_favorite_order handler")
        
        # Check if there are any import issues
        from src.data_storage import delete_favorite_order as handler_delete_function
        print("   Successfully imported delete function from handlers perspective")
        
    except Exception as e:
        print(f"   ❌ Handler import error: {e}")

def test_handler_simulation():
    """Simulate the exact handler flow"""
    print(f"\n5. SIMULATING EXACT HANDLER FLOW")

    test_user_id = 123456789

    # This simulates what happens when user clicks delete
    try:
        # Get favorites (this should load cache)
        favorites = get_user_favorite_orders(test_user_id)
        print(f"   Handler got {len(favorites)} favorites")

        if len(favorites) > 0:
            # Try to delete index 0
            success = delete_favorite_order(test_user_id, 0)
            print(f"   Handler deletion result: {success}")

            if success:
                # Get updated list
                updated_favorites = get_user_favorite_orders(test_user_id)
                print(f"   Updated favorites count: {len(updated_favorites)}")
                print("   ✅ Handler simulation successful")
            else:
                print("   ❌ Handler simulation failed at deletion")
        else:
            print("   ❌ Handler simulation failed - no favorites found")

    except Exception as e:
        print(f"   ❌ Handler simulation exception: {e}")
        import traceback
        traceback.print_exc()

def test_handler_function_fix():
    """Test the specific handler function issue that was fixed"""
    print(f"\n6. TESTING HANDLER FUNCTION FIX")

    test_user_id = 123456789

    # Set up test data again
    test_favorites = [
        {
            "favorite_name": "Test Handler Order",
            "items": [{"name": "Test Item", "price": 50}],
            "total": 50,
            "timestamp": "2025-09-06 13:00:00"
        }
    ]

    # Clear cache and set up fresh data
    favorite_orders.clear()
    firebase_result = update_favorite_orders(str(test_user_id), test_favorites)
    print(f"   Set up test data: {firebase_result}")

    # Test the old problematic function call
    try:
        from src.firebase_db import get_favorite_orders
        old_result = get_favorite_orders(str(test_user_id))
        print(f"   Old function result type: {type(old_result)}")
        print(f"   Old function result: {old_result}")

        # This would have caused the error - trying to iterate over dict
        try:
            for i, favorite in enumerate(old_result):
                print(f"   This would fail: {i}, {favorite}")
        except Exception as e:
            print(f"   ❌ Old function iteration error (expected): {e}")

    except Exception as e:
        print(f"   ❌ Old function error: {e}")

    # Test the new correct function call
    try:
        new_result = get_user_favorite_orders(test_user_id)
        print(f"   New function result type: {type(new_result)}")
        print(f"   New function result length: {len(new_result)}")

        # This should work fine
        for i, favorite in enumerate(new_result):
            print(f"   ✅ New function iteration works: {i}, {favorite.get('favorite_name')}")

    except Exception as e:
        print(f"   ❌ New function error: {e}")

if __name__ == "__main__":
    test_real_user_scenario()
    test_handler_simulation()
    test_handler_function_fix()

    print("\n" + "="*60)
    print("DEBUG COMPLETED")
    print("="*60)
